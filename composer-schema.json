{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "keywords": {"type": "array"}, "license": {"type": "string"}, "require": {"type": "object"}, "require-dev": {"type": "object"}, "autoload": {"type": "object"}, "autoload-dev": {"type": "object"}, "scripts": {"type": "object"}, "extra": {"type": "object"}, "config": {"type": "object"}, "minimum-stability": {"type": "string"}, "prefer-stable": {"type": "boolean"}}}